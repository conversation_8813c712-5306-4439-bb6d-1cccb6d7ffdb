<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>DeepSeek Market Analysis Dashboard</title>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/Chart.js/3.9.1/chart.min.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: #333;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            text-align: center;
            margin-bottom: 30px;
            color: white;
        }

        .header h1 {
            font-size: 2.5rem;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }

        .dashboard {
            display: grid;
            grid-template-columns: 1fr 2fr;
            gap: 20px;
            margin-bottom: 20px;
        }

        .panel {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            padding: 25px;
            box-shadow: 0 8px 32px rgba(31, 38, 135, 0.37);
            border: 1px solid rgba(255, 255, 255, 0.18);
        }

        .panel h2 {
            color: #4a5568;
            margin-bottom: 20px;
            font-size: 1.4rem;
            border-bottom: 2px solid #e2e8f0;
            padding-bottom: 10px;
        }

        .form-group {
            margin-bottom: 20px;
        }

        label {
            display: block;
            margin-bottom: 8px;
            font-weight: 600;
            color: #2d3748;
        }

        input, textarea, select {
            width: 100%;
            padding: 12px;
            border: 2px solid #e2e8f0;
            border-radius: 8px;
            font-size: 14px;
            transition: all 0.3s ease;
        }

        input:focus, textarea:focus, select:focus {
            outline: none;
            border-color: #667eea;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }

        textarea {
            resize: vertical;
            min-height: 120px;
        }

        .button {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 16px;
            font-weight: 600;
            transition: all 0.3s ease;
            width: 100%;
        }

        .button:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
        }

        .button:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }

        .results-section {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin-top: 20px;
        }

        .chart-container {
            height: 400px;
            position: relative;
        }

        .analysis-output {
            background: #f7fafc;
            border-left: 4px solid #667eea;
            padding: 20px;
            border-radius: 8px;
            margin-top: 15px;
        }

        .analysis-output h3 {
            color: #2d3748;
            margin-bottom: 15px;
        }

        .analysis-output p {
            line-height: 1.6;
            margin-bottom: 10px;
        }

        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
        }

        .status-connected { background-color: #48bb78; }
        .status-disconnected { background-color: #f56565; }
        .status-processing { background-color: #ed8936; }

        .market-metrics {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-bottom: 20px;
        }

        .metric-card {
            background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
            padding: 15px;
            border-radius: 10px;
            text-align: center;
            border: 1px solid #e2e8f0;
        }

        .metric-value {
            font-size: 1.8rem;
            font-weight: bold;
            color: #2d3748;
        }

        .metric-label {
            font-size: 0.9rem;
            color: #718096;
            margin-top: 5px;
        }

        .loading {
            display: none;
            text-align: center;
            padding: 20px;
        }

        .spinner {
            border: 4px solid #f3f3f3;
            border-top: 4px solid #667eea;
            border-radius: 50%;
            width: 40px;
            height: 40px;
            animation: spin 1s linear infinite;
            margin: 0 auto 10px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .action-buttons {
            display: flex;
            gap: 10px;
            margin-top: 15px;
        }

        .action-btn {
            flex: 1;
            padding: 10px;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            font-weight: 600;
            transition: all 0.3s ease;
        }

        .buy-btn {
            background-color: #48bb78;
            color: white;
        }

        .sell-btn {
            background-color: #f56565;
            color: white;
        }

        .hold-btn {
            background-color: #ed8936;
            color: white;
        }

        .action-btn:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
        }

        @media (max-width: 768px) {
            .dashboard {
                grid-template-columns: 1fr;
            }
            
            .results-section {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🚀 DeepSeek Market Analysis Dashboard</h1>
            <p>AI-Powered Trading Strategy Assistant</p>
        </div>

        <div class="dashboard">
            <div class="panel">
                <h2>Configuration</h2>
                
                <div class="form-group">
                    <label for="apiKey">DeepSeek API Key:</label>
                    <input type="password" id="apiKey" placeholder="Enter your DeepSeek API key">
                </div>

                <div class="form-group">
                    <label for="symbol">Trading Symbol:</label>
                    <input type="text" id="symbol" placeholder="e.g., BTCUSDT, AAPL, TSLA" value="BTCUSDT">
                </div>

                <div class="form-group">
                    <label for="timeframe">Timeframe:</label>
                    <select id="timeframe">
                        <option value="1m">1 Minute</option>
                        <option value="5m">5 Minutes</option>
                        <option value="15m">15 Minutes</option>
                        <option value="30m">30 Minutes</option>
                        <option value="45m">45 Minutes</option>
                        <option value="1h" selected>1 Hour</option>
                        <option value="4h">4 Hours</option>
                        <option value="1d">1 Day</option>
                        <option value="7d">1 Week</option>						
                    </select>
                </div>

                <div class="form-group">
                    <label for="riskTolerance">Risk Tolerance:</label>
                    <select id="riskTolerance">
                        <option value="conservative">Conservative</option>
                        <option value="moderate" selected>Moderate</option>
                        <option value="aggressive">Aggressive</option>
                    </select>
                </div>

                <div class="form-group">
                    <label for="customPrompt">Strategy Prompt:</label>
                    <textarea id="customPrompt" placeholder="Enter your custom analysis prompt...">You are an expert trading analyst. Analyze the provided market data for {symbol} and provide:

1. Technical Analysis:
   - Support and resistance levels
   - Trend direction and strength
   - Key indicators (RSI, MACD, Moving averages)

2. Market Sentiment:
   - Current market conditions
   - Volume analysis
   - Price action patterns

3. Risk Assessment:
   - Potential risks and opportunities
   - Volatility analysis
   - Market correlation factors

4. Trading Recommendation:
   - Clear BUY/SELL/HOLD recommendation
   - Entry and exit points
   - Stop loss and take profit levels
   - Position sizing advice

5. Reasoning:
   - Detailed explanation of your analysis
   - Supporting evidence for recommendation
   - Alternative scenarios to consider

Please be specific and actionable in your recommendations.</textarea>
                </div>

                <button class="button" onclick="startAnalysis()">
                    <span class="status-indicator" id="connectionStatus"></span>
                    Start Analysis
                </button>
            </div>

            <div class="panel">
                <h2>Market Overview</h2>
                
                <div class="market-metrics">
                    <div class="metric-card">
                        <div class="metric-value" id="currentPrice">$0.00</div>
                        <div class="metric-label">Current Price</div>
                    </div>
                    <div class="metric-card">
                        <div class="metric-value" id="priceChange">0.00%</div>
                        <div class="metric-label">24h Change</div>
                    </div>
                    <div class="metric-card">
                        <div class="metric-value" id="volume">0</div>
                        <div class="metric-label">Volume</div>
                    </div>
                    <div class="metric-card">
                        <div class="metric-value" id="marketCap">$0</div>
                        <div class="metric-label">Market Cap</div>
                    </div>
                </div>

                <div class="chart-container">
                    <canvas id="priceChart"></canvas>
                </div>
            </div>
        </div>

        <div class="results-section">
            <div class="panel">
                <h2>AI Analysis Results</h2>
                
                <div class="loading" id="loadingIndicator">
                    <div class="spinner"></div>
                    <p>Analyzing market data with DeepSeek AI...</p>
                </div>

                <div class="analysis-output" id="analysisResults">
                    <h3>📊 Waiting for Analysis</h3>
                    <p>Configure your settings and click "Start Analysis" to get AI-powered market insights.</p>
                </div>

                <div class="action-buttons" id="actionButtons" style="display: none;">
                    <button class="action-btn buy-btn" onclick="executeAction('BUY')">🚀 BUY</button>
                    <button class="action-btn hold-btn" onclick="executeAction('HOLD')">⏸️ HOLD</button>
                    <button class="action-btn sell-btn" onclick="executeAction('SELL')">📉 SELL</button>
                </div>
            </div>

            <div class="panel">
                <h2>Trading Signals</h2>
                
                <div id="tradingSignals">
                    <div class="analysis-output">
                        <h3>🎯 Signal Generator</h3>
                        <p>Real-time trading signals will appear here based on AI analysis.</p>
                        <ul id="signalsList" style="margin-top: 15px; padding-left: 20px;">
                            <li>Waiting for market data...</li>
                        </ul>
                    </div>
                </div>

                <div style="margin-top: 20px;">
                    <h3>📈 Technical Indicators</h3>
                    <div id="technicalIndicators">
                        <p><strong>RSI:</strong> <span id="rsiValue">--</span></p>
                        <p><strong>MACD:</strong> <span id="macdValue">--</span></p>
                        <p><strong>Moving Average (20):</strong> <span id="ma20Value">--</span></p>
                        <p><strong>Bollinger Bands:</strong> <span id="bbValue">--</span></p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        let priceChart;
        let isAnalyzing = false;

        // Initialize the dashboard
        function initializeDashboard() {
            updateConnectionStatus('disconnected');
            generateMockData();
            initializePriceChart();
        }

        // Update connection status
        function updateConnectionStatus(status) {
            const statusElement = document.getElementById('connectionStatus');
            statusElement.className = `status-indicator status-${status}`;
        }

        // Generate mock market data for demonstration
        function generateMockData() {
            const basePrice = 45000 + Math.random() * 10000;
            const priceChange = (Math.random() - 0.5) * 10;
            
            document.getElementById('currentPrice').textContent = `$${basePrice.toFixed(2)}`;
            document.getElementById('priceChange').textContent = `${priceChange.toFixed(2)}%`;
            document.getElementById('priceChange').style.color = priceChange >= 0 ? '#48bb78' : '#f56565';
            document.getElementById('volume').textContent = `${(Math.random() * 1000000).toFixed(0)}`;
            document.getElementById('marketCap').textContent = `$${(basePrice * 19000000 / 1000000000).toFixed(1)}B`;

            // Update technical indicators
            document.getElementById('rsiValue').textContent = (Math.random() * 100).toFixed(1);
            document.getElementById('macdValue').textContent = (Math.random() * 2 - 1).toFixed(3);
            document.getElementById('ma20Value').textContent = `$${(basePrice * (0.98 + Math.random() * 0.04)).toFixed(2)}`;
            document.getElementById('bbValue').textContent = `$${(basePrice * 0.95).toFixed(2)} - $${(basePrice * 1.05).toFixed(2)}`;
        }

        // Initialize price chart
        function initializePriceChart() {
            const ctx = document.getElementById('priceChart').getContext('2d');
            
            // Generate sample price data
            const labels = [];
            const data = [];
            const basePrice = 45000;
            
            for (let i = 30; i >= 0; i--) {
                const date = new Date();
                date.setMinutes(date.getMinutes() - i * 15);
                labels.push(date.toLocaleTimeString([], {hour: '2-digit', minute:'2-digit'}));
                
                const price = basePrice + (Math.random() - 0.5) * 5000 + Math.sin(i / 5) * 2000;
                data.push(price);
            }

            priceChart = new Chart(ctx, {
                type: 'line',
                data: {
                    labels: labels,
                    datasets: [{
                        label: 'Price',
                        data: data,
                        borderColor: '#667eea',
                        backgroundColor: 'rgba(102, 126, 234, 0.1)',
                        borderWidth: 2,
                        fill: true,
                        tension: 0.4
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            display: false
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: false,
                            ticks: {
                                callback: function(value) {
                                    return '$' + value.toLocaleString();
                                }
                            }
                        }
                    }
                }
            });
        }

        // Start analysis with DeepSeek API
        async function startAnalysis() {
            if (isAnalyzing) return;
            
            const apiKey = document.getElementById('apiKey').value;
            const symbol = document.getElementById('symbol').value;
            const customPrompt = document.getElementById('customPrompt').value;
            
            if (!apiKey || !symbol) {
                alert('Please enter API key and trading symbol');
                return;
            }

            isAnalyzing = true;
            updateConnectionStatus('processing');
            document.getElementById('loadingIndicator').style.display = 'block';
            document.getElementById('analysisResults').style.display = 'none';

            try {
                // Simulate API call (replace with actual DeepSeek API integration)
                await simulateDeepSeekAnalysis(symbol, customPrompt);
                
                updateConnectionStatus('connected');
                document.getElementById('actionButtons').style.display = 'flex';
                
            } catch (error) {
                console.error('Analysis failed:', error);
                updateConnectionStatus('disconnected');
                showAnalysisResults('❌ Analysis Failed', 'Unable to connect to DeepSeek API. Please check your API key and try again.');
            } finally {
                isAnalyzing = false;
                document.getElementById('loadingIndicator').style.display = 'none';
                document.getElementById('analysisResults').style.display = 'block';
            }
        }

        // Simulate DeepSeek API analysis (replace with actual API call)
        async function simulateDeepSeekAnalysis(symbol, prompt) {
            // Simulate API delay
            await new Promise(resolve => setTimeout(resolve, 3000));
            
            // Generate realistic analysis results
            const analyses = [
                {
                    title: '🎯 BULLISH Signal Detected',
                    content: `Strong upward momentum detected for ${symbol}. Technical indicators show:
                    
• RSI at 65 - Still room for growth
• MACD showing positive divergence
• Breaking above 20-day moving average
• Volume increasing on green candles

Recommendation: BUY with stop loss at $42,500 and take profit at $52,000.`
                },
                {
                    title: '⚠️ BEARISH Trend Warning',
                    content: `Concerning signals emerging for ${symbol}:
                    
• RSI showing overbought conditions at 78
• Decreasing volume on recent pumps
• Failed to break key resistance at $48,000
• MACD histogram showing weakness

Recommendation: SELL or wait for better entry point. Consider shorting with tight stop loss.`
                },
                {
                    title: '⏸️ SIDEWAYS Consolidation',
                    content: `${symbol} is in a consolidation phase:
                    
• Trading within established range $44,000-$47,000
• Low volatility and decreasing volume
• Waiting for catalyst to break range
• Mixed signals from technical indicators

Recommendation: HOLD current positions and wait for clear breakout direction.`
                }
            ];
            
            const randomAnalysis = analyses[Math.floor(Math.random() * analyses.length)];
            showAnalysisResults(randomAnalysis.title, randomAnalysis.content);
            
            // Update trading signals
            updateTradingSignals();
        }

        // Show analysis results
        function showAnalysisResults(title, content) {
            const resultsDiv = document.getElementById('analysisResults');
            resultsDiv.innerHTML = `
                <h3>${title}</h3>
                <p style="white-space: pre-line;">${content}</p>
                <div style="margin-top: 15px; padding: 10px; background: rgba(102, 126, 234, 0.1); border-radius: 6px;">
                    <small><strong>AI Confidence:</strong> ${(Math.random() * 30 + 70).toFixed(1)}% | <strong>Generated:</strong> ${new Date().toLocaleTimeString()}</small>
                </div>
            `;
        }

        // Update trading signals
        function updateTradingSignals() {
            const signals = [
                '📈 Golden Cross forming on 4h timeframe',
                '🎯 Support holding strong at $44,200',
                '⚡ High volume breakout above $46,500',
                '📊 Fibonacci retracement at 61.8%',
                '🔥 Momentum oscillators turning bullish'
            ];
            
            const signalsList = document.getElementById('signalsList');
            signalsList.innerHTML = signals.map(signal => `<li>${signal}</li>`).join('');
        }

        // Execute trading action
        function executeAction(action) {
            const symbol = document.getElementById('symbol').value;
            alert(`${action} action logged for ${symbol}!\n\nThis is a demo. In a real implementation, this would:\n• Place orders through your broker API\n• Log the action to your trading journal\n• Update portfolio tracking\n• Send notifications`);
        }

        // Auto-refresh data every 30 seconds
        setInterval(() => {
            if (!isAnalyzing) {
                generateMockData();
                
                // Update chart with new data point
                if (priceChart) {
                    const newTime = new Date().toLocaleTimeString([], {hour: '2-digit', minute:'2-digit'});
                    const newPrice = 45000 + (Math.random() - 0.5) * 8000;
                    
                    priceChart.data.labels.push(newTime);
                    priceChart.data.datasets[0].data.push(newPrice);
                    
                    // Keep only last 30 data points
                    if (priceChart.data.labels.length > 30) {
                        priceChart.data.labels.shift();
                        priceChart.data.datasets[0].data.shift();
                    }
                    
                    priceChart.update('none');
                }
            }
        }, 30000);

        // Initialize dashboard on page load
        document.addEventListener('DOMContentLoaded', initializeDashboard);
    </script>
</body>
</html>